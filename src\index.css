@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 208 25% 20%;

    --card: 0 0% 100%;
    --card-foreground: 208 25% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 208 25% 20%;

    --primary: 195 85% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 195 15% 95%;
    --secondary-foreground: 208 25% 20%;

    --muted: 195 15% 96%;
    --muted-foreground: 208 15% 50%;

    --accent: 195 55% 85%;
    --accent-foreground: 208 25% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 195 15% 90%;
    --input: 195 15% 94%;
    --ring: 195 85% 41%;

    --radius: 0.75rem;

    /* Hala Brand Colors */
    --brand-teal: 195 85% 41%;
    --brand-teal-light: 195 55% 85%;
    --brand-teal-dark: 195 85% 35%;
    --brand-ocean: 200 40% 60%;
    --brand-warm: 45 30% 92%;

    /* Gradients */
    --gradient-ocean: linear-gradient(
      135deg,
      hsl(var(--brand-teal)) 0%,
      hsl(var(--brand-ocean)) 100%
    );
    --gradient-warm: linear-gradient(
      135deg,
      hsl(var(--brand-warm)) 0%,
      hsl(var(--background)) 100%
    );

    /* Shadows */
    --shadow-soft: 0 4px 20px -4px hsl(var(--brand-teal) / 0.15);
    --shadow-elevated: 0 8px 30px -8px hsl(var(--brand-teal) / 0.25);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 208 30% 8%;
    --foreground: 0 0% 98%;

    --card: 208 25% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 208 25% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 195 85% 65%;
    --primary-foreground: 208 25% 15%;

    --secondary: 208 15% 18%;
    --secondary-foreground: 0 0% 90%;

    --muted: 208 15% 15%;
    --muted-foreground: 208 10% 60%;

    --accent: 195 35% 25%;
    --accent-foreground: 0 0% 90%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 98%;

    --border: 208 15% 20%;
    --input: 208 15% 18%;
    --ring: 195 85% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Almarai", sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* All text now uses Almarai font by default */

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow: 0 0 20px rgba(var(--brand-teal-rgb), 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(var(--brand-teal-rgb), 0.6);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* New enhanced animations for features section */
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes gradient-shift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-elevated);
  }

  /* Enhanced card effects */
  .card-perspective {
    perspective: 1000px;
  }

  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .card-3d:hover {
    transform: rotateY(5deg) rotateX(5deg);
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  [dir="rtl"] .justify-start {
    justify-content: flex-end;
  }

  [dir="rtl"] .justify-end {
    justify-content: flex-start;
  }

  /* Almarai font is now the default for all text, including RTL */

  /* Adjust spacing for Arabic text */
  [dir="rtl"] .tracking-tight {
    letter-spacing: 0;
  }

  [dir="rtl"] .tracking-wide {
    letter-spacing: 0.025em;
  }

  /* Modern Calendar Styles - Force Override */
  .calendar-modern {
    --calendar-bg: #ffffff;
    --calendar-border: #e5e7eb;
    --calendar-text: #374151;
    --calendar-muted: #6b7280;
    --calendar-accent: #2563eb;
    --calendar-accent-fg: #ffffff;
    --calendar-today-bg: #dbeafe;
    --calendar-today-fg: #1e40af;
    --calendar-hover-bg: #eff6ff;
    --calendar-hover-fg: #1e40af;
    background-color: white;
    border-radius: 1rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
      0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }

  /* Ultra-specific overrides to force size changes */
  .calendar-modern .rdp .rdp-month_grid .rdp-weekdays .rdp-weekday,
  .calendar-modern .rdp .rdp-month_grid .rdp-week .rdp-day,
  .calendar-modern .rdp .rdp-month_grid .rdp-week .rdp-day .rdp-day_button {
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    max-width: 40px !important;
    max-height: 40px !important;
  }

  .calendar-modern .rdp .rdp-month_grid .rdp-weekdays,
  .calendar-modern .rdp .rdp-month_grid .rdp-week {
    display: grid !important;
    grid-template-columns: repeat(7, minmax(40px, 1fr)) !important;
    gap: 8px !important;
  }

  .calendar-modern {
    overflow: hidden !important;
    width: fit-content !important;
    padding: 0.5rem;
  }

  .calendar-modern .rdp {
    margin: 0;
  }

  .calendar-modern .rdp-months {
    position: relative;
  }

  .calendar-modern .rdp-month {
    width: 100%;
  }

  .calendar-modern .rdp-month_caption {
    display: flex !important;
    justify-content: center !important;
    padding: 0.5rem 0 !important;
    align-items: center !important;
    position: relative !important;
    margin-bottom: 1rem !important;
    padding: 0.5rem !important;
  }

  .calendar-modern .rdp-caption_label {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: var(--calendar-text) !important;
  }

  .calendar-modern .rdp-nav {
    position: absolute !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
  }

  .calendar-modern .rdp-button_previous {
    left: 0.25rem !important;
    position: absolute !important;
    width: 2rem !important;
    height: 2rem !important;
    border-radius: 50% !important;
    border: none !important;
    background: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    opacity: 0.9 !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    color: var(--calendar-accent) !important;
  }

  .calendar-modern .rdp-button_next {
    right: 0.25rem !important;
    position: absolute !important;
    width: 2rem !important;
    height: 2rem !important;
    border-radius: 50% !important;
    border: none !important;
    background: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    opacity: 0.9 !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    color: var(--calendar-accent) !important;
  }

  .calendar-modern .rdp-button_previous:hover,
  .calendar-modern .rdp-button_next:hover {
    opacity: 1 !important;
    background-color: var(--calendar-accent) !important;
    color: white !important;
    transform: scale(1.1) !important;
  }

  .calendar-modern .rdp-month_grid {
    width: 100% !important;
  }

  .calendar-modern .rdp-weekdays {
    display: grid !important;
    grid-template-columns: repeat(7, 1fr) !important;
    gap: 0.25rem !important;
    margin-bottom: 0.5rem !important;
  }

  .calendar-modern .rdp-weekday {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 2rem !important;
    height: 2rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    color: var(--calendar-muted) !important;
    text-align: center !important;
  }

  .calendar-modern .rdp-week {
    display: grid !important;
    grid-template-columns: repeat(7, 1fr) !important;
    gap: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .calendar-modern .rdp-day {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 2rem !important;
    height: 2rem !important;
    position: relative !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .calendar-modern .rdp-day_button {
    width: 2.5rem !important;
    height: 2.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 9999px !important;
    border: none !important;
    background: transparent !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: var(--calendar-text) !important;
    cursor: pointer !important;
    transition: all 0.2s ease-in-out !important;
  }

  .calendar-modern .rdp-day_button:hover {
    background-color: var(--calendar-hover-bg) !important;
    color: var(--calendar-hover-fg) !important;
    transform: scale(1.05) !important;
  }

  .calendar-modern .rdp-day_selected .rdp-day_button {
    background-color: var(--calendar-accent) !important;
    color: var(--calendar-accent-fg) !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.2),
      0 2px 4px -1px rgba(37, 99, 235, 0.1) !important;
  }

  .calendar-modern .rdp-day_selected .rdp-day_button:hover {
    background-color: #1d4ed8 !important;
    color: var(--calendar-accent-fg) !important;
    transform: scale(1.05) !important;
  }

  .calendar-modern .rdp-day_today .rdp-day_button {
    background-color: var(--calendar-today-bg) !important;
    color: var(--calendar-today-fg) !important;
    font-weight: 600 !important;
    box-shadow: 0 0 0 2px var(--calendar-today-fg) !important;
  }

  .calendar-modern .rdp-day_outside .rdp-day_button {
    color: #9ca3af !important;
    opacity: 0.5 !important;
  }

  .calendar-modern .rdp-day_disabled .rdp-day_button {
    color: #d1d5db !important;
    opacity: 0.4 !important;
    cursor: not-allowed !important;
    text-decoration: line-through !important;
    background-color: #f3f4f6 !important;
  }

  .calendar-modern .rdp-day_disabled .rdp-day_button:hover {
    background-color: #f3f4f6 !important;
    transform: none !important;
    cursor: not-allowed !important;
  }
}

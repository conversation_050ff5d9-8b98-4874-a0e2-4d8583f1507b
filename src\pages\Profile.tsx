import { useTranslation } from "react-i18next";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Calendar,
  Settings,
  LogOut,
  ArrowLeft,
  Camera,
  Bell,
  Shield,
  Globe,
  Palette,
  Activity,
  Heart,
  Star,
  MapPin,
  Phone,
  Edit3,
  Save,
  X,
} from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import logoImage from "@/assets/logo.png";

const Profile = () => {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const { isRTL } = useLanguage();

  // State for editing modes
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingPassword, setIsEditingPassword] = useState(false);

  // Form states
  const [profileData, setProfileData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    phone: "",
    bio: "",
    location: "",
  });

  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    pushNotifications: false,
    marketingEmails: false,
    darkMode: false,
    language: "en",
  });

  const handleLogout = () => {
    logout();
  };

  const handleProfileSave = () => {
    // Here you would typically save to backend
    setIsEditingProfile(false);
  };

  const handlePasswordSave = () => {
    // Here you would typically save to backend
    setIsEditingPassword(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center group cursor-pointer">
                <img
                  src={logoImage}
                  alt="Hala Logo"
                  className="w-8 h-8 object-contain transition-transform duration-300 group-hover:scale-105"
                />
                <span className="ml-2 text-lg font-bold text-gray-900 hidden sm:block">
                  Hala
                </span>
              </Link>
            </div>
            <Link to="/">
              <Button
                variant="ghost"
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-brand-teal bg-clip-text text-transparent mb-4">
            {t("auth.profile")}
          </h1>
          <p className="text-muted-foreground text-lg">
            Manage your account settings and preferences
          </p>
        </div>

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Security
            </TabsTrigger>
            <TabsTrigger
              value="preferences"
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Preferences
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Activity
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Enhanced Profile Card */}
              <div className="lg:col-span-1">
                <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
                  <CardHeader className="text-center pb-4">
                    <div className="relative flex justify-center mb-4">
                      <div className="relative group">
                        <Avatar className="w-32 h-32 border-4 border-primary/20 shadow-elevated">
                          <AvatarImage src={user?.avatar} alt={user?.name} />
                          <AvatarFallback className="bg-gradient-to-br from-primary to-brand-teal text-white text-2xl font-bold">
                            {user?.name?.charAt(0) || "U"}
                          </AvatarFallback>
                        </Avatar>
                        <Button
                          size="icon"
                          variant="secondary"
                          className="absolute bottom-2 right-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        >
                          <Camera className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <CardTitle className="text-2xl font-bold">
                      {user?.name}
                    </CardTitle>
                    <CardDescription className="text-muted-foreground flex items-center justify-center gap-2">
                      <Mail className="h-4 w-4" />
                      {user?.email}
                    </CardDescription>
                    <div className="flex justify-center gap-2 mt-4">
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-700"
                      >
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        Verified
                      </Badge>
                      <Badge variant="outline">Premium</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                        <Calendar className="h-5 w-5 text-primary" />
                        <div>
                          <p className="text-sm font-medium">Member since</p>
                          <p className="text-sm text-muted-foreground">
                            January 2024
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                        <Activity className="h-5 w-5 text-primary" />
                        <div>
                          <p className="text-sm font-medium">Profile views</p>
                          <p className="text-sm text-muted-foreground">
                            127 this month
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors">
                        <Heart className="h-5 w-5 text-primary" />
                        <div>
                          <p className="text-sm font-medium">
                            Saved properties
                          </p>
                          <p className="text-sm text-muted-foreground">
                            8 favorites
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Enhanced Account Settings */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <User className="h-5 w-5 text-primary" />
                          Personal Information
                        </CardTitle>
                        <CardDescription>
                          Update your personal details and contact information
                        </CardDescription>
                      </div>
                      <Button
                        variant={isEditingProfile ? "outline" : "ghost"}
                        size="sm"
                        onClick={() => setIsEditingProfile(!isEditingProfile)}
                        className="gap-2"
                      >
                        {isEditingProfile ? (
                          <>
                            <X className="h-4 w-4" />
                            Cancel
                          </>
                        ) : (
                          <>
                            <Edit3 className="h-4 w-4" />
                            Edit
                          </>
                        )}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <Input
                          id="name"
                          value={profileData.name}
                          onChange={(e) =>
                            setProfileData({
                              ...profileData,
                              name: e.target.value,
                            })
                          }
                          disabled={!isEditingProfile}
                          className="transition-all duration-200"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          type="email"
                          value={profileData.email}
                          onChange={(e) =>
                            setProfileData({
                              ...profileData,
                              email: e.target.value,
                            })
                          }
                          disabled={!isEditingProfile}
                          className="transition-all duration-200"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          type="tel"
                          value={profileData.phone}
                          onChange={(e) =>
                            setProfileData({
                              ...profileData,
                              phone: e.target.value,
                            })
                          }
                          disabled={!isEditingProfile}
                          placeholder="+****************"
                          className="transition-all duration-200"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={profileData.location}
                          onChange={(e) =>
                            setProfileData({
                              ...profileData,
                              location: e.target.value,
                            })
                          }
                          disabled={!isEditingProfile}
                          placeholder="City, Country"
                          className="transition-all duration-200"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bio">Bio</Label>
                      <Textarea
                        id="bio"
                        value={profileData.bio}
                        onChange={(e) =>
                          setProfileData({
                            ...profileData,
                            bio: e.target.value,
                          })
                        }
                        disabled={!isEditingProfile}
                        placeholder="Tell us about yourself..."
                        className="min-h-[100px] transition-all duration-200"
                      />
                    </div>
                    {isEditingProfile && (
                      <div className="flex justify-end gap-3">
                        <Button
                          variant="outline"
                          onClick={() => setIsEditingProfile(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleProfileSave} className="gap-2">
                          <Save className="h-4 w-4" />
                          Save Changes
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-primary" />
                      Quick Actions
                    </CardTitle>
                    <CardDescription>
                      Frequently used actions and shortcuts
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        className="h-20 flex-col gap-2 hover:bg-primary/5"
                      >
                        <Heart className="h-6 w-6 text-primary" />
                        <span className="text-sm">Saved Properties</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex-col gap-2 hover:bg-primary/5"
                      >
                        <Activity className="h-6 w-6 text-primary" />
                        <span className="text-sm">View History</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex-col gap-2 hover:bg-primary/5"
                      >
                        <MapPin className="h-6 w-6 text-primary" />
                        <span className="text-sm">Saved Searches</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex-col gap-2 hover:bg-primary/5"
                      >
                        <Bell className="h-6 w-6 text-primary" />
                        <span className="text-sm">Notifications</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5 text-primary" />
                      Password & Security
                    </CardTitle>
                    <CardDescription>
                      Manage your password and security settings
                    </CardDescription>
                  </div>
                  <Button
                    variant={isEditingPassword ? "outline" : "ghost"}
                    size="sm"
                    onClick={() => setIsEditingPassword(!isEditingPassword)}
                    className="gap-2"
                  >
                    {isEditingPassword ? (
                      <>
                        <X className="h-4 w-4" />
                        Cancel
                      </>
                    ) : (
                      <>
                        <Edit3 className="h-4 w-4" />
                        Change Password
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {isEditingPassword ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input
                        id="current-password"
                        type="password"
                        placeholder="Enter current password"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="new-password">New Password</Label>
                        <Input
                          id="new-password"
                          type="password"
                          placeholder="Enter new password"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">
                          Confirm Password
                        </Label>
                        <Input
                          id="confirm-password"
                          type="password"
                          placeholder="Confirm new password"
                        />
                      </div>
                    </div>
                    <div className="flex justify-end gap-3">
                      <Button
                        variant="outline"
                        onClick={() => setIsEditingPassword(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handlePasswordSave} className="gap-2">
                        <Save className="h-4 w-4" />
                        Update Password
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Shield className="h-5 w-5 text-green-600" />
                        <div>
                          <p className="font-medium">Password</p>
                          <p className="text-sm text-muted-foreground">
                            Last updated 3 months ago
                          </p>
                        </div>
                      </div>
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-700"
                      >
                        Strong
                      </Badge>
                    </div>
                  </div>
                )}

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Two-Factor Authentication</h4>
                  <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-primary" />
                      <div>
                        <p className="font-medium">SMS Authentication</p>
                        <p className="text-sm text-muted-foreground">
                          Receive codes via SMS
                        </p>
                      </div>
                    </div>
                    <Switch />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preferences Tab */}
          <TabsContent value="preferences" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5 text-primary" />
                    Notifications
                  </CardTitle>
                  <CardDescription>
                    Manage how you receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Email Notifications</p>
                      <p className="text-sm text-muted-foreground">
                        Receive updates via email
                      </p>
                    </div>
                    <Switch
                      checked={preferences.emailNotifications}
                      onCheckedChange={(checked) =>
                        setPreferences({
                          ...preferences,
                          emailNotifications: checked,
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Push Notifications</p>
                      <p className="text-sm text-muted-foreground">
                        Receive push notifications
                      </p>
                    </div>
                    <Switch
                      checked={preferences.pushNotifications}
                      onCheckedChange={(checked) =>
                        setPreferences({
                          ...preferences,
                          pushNotifications: checked,
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Marketing Emails</p>
                      <p className="text-sm text-muted-foreground">
                        Receive promotional content
                      </p>
                    </div>
                    <Switch
                      checked={preferences.marketingEmails}
                      onCheckedChange={(checked) =>
                        setPreferences({
                          ...preferences,
                          marketingEmails: checked,
                        })
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-primary" />
                    Appearance
                  </CardTitle>
                  <CardDescription>Customize your experience</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Dark Mode</p>
                      <p className="text-sm text-muted-foreground">
                        Switch to dark theme
                      </p>
                    </div>
                    <Switch
                      checked={preferences.darkMode}
                      onCheckedChange={(checked) =>
                        setPreferences({ ...preferences, darkMode: checked })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Language</Label>
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">English (US)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-0 bg-gradient-to-br from-background to-brand-warm/30 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Your recent actions and interactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      action: "Viewed property",
                      details: "Modern Apartment in Downtown",
                      time: "2 hours ago",
                      icon: "👁️",
                    },
                    {
                      action: "Saved property",
                      details: "Luxury Villa with Pool",
                      time: "1 day ago",
                      icon: "❤️",
                    },
                    {
                      action: "Updated profile",
                      details: "Changed profile picture",
                      time: "3 days ago",
                      icon: "✏️",
                    },
                    {
                      action: "Contacted agent",
                      details: "Inquired about Beach House",
                      time: "1 week ago",
                      icon: "📞",
                    },
                  ].map((activity, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors"
                    >
                      <div className="text-2xl">{activity.icon}</div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-muted-foreground">
                          {activity.details}
                        </p>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {activity.time}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Danger Zone */}
            <Card className="shadow-soft hover:shadow-elevated transition-all duration-300 border-destructive/20 bg-gradient-to-br from-background to-destructive/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-destructive flex items-center gap-2">
                  <LogOut className="h-5 w-5" />
                  Account Actions
                </CardTitle>
                <CardDescription>Manage your account status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                    <div>
                      <h3 className="font-medium text-destructive">Sign Out</h3>
                      <p className="text-sm text-muted-foreground">
                        Sign out of your account on this device
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      onClick={handleLogout}
                      className="border-destructive/30 text-destructive hover:bg-destructive/10 gap-2"
                    >
                      <LogOut className="h-4 w-4" />
                      Sign Out
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Profile;
